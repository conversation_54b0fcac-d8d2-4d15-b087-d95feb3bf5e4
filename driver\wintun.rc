/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2018-2021 WireGuard LLC. All Rights Reserved.
 */

#include <windows.h>
#include <ntverp.h>

#pragma code_page(1252)

#define STRINGIZE(x) #x
#define EXPAND(x) STRINGIZE(x)

VS_VERSION_INFO VERSIONINFO
FILEVERSION    WINTUN_VERSION_MAJ, WINTUN_VERSION_MIN, WINTUN_VERSION_REL, 0
PRODUCTVERSION WINTUN_VERSION_MAJ, WINTUN_VERSION_MIN, WINTUN_VERSION_REL, 0
FILEOS         VOS_NT_WINDOWS32
FILETYPE       VFT_DRV
FILESUBTYPE    VFT2_DRV_NETWORK
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904b0"
    BEGIN
      VALUE "CompanyName", "WireGuard LLC"
      VALUE "FileDescription", "Wintun Driver"
      VALUE "FileVersion", EXPAND(WINTUN_VERSION)
      VALUE "InternalName", "wintun.sys"
      VALUE "LegalCopyright", "Copyright \xa9 2018-2021 WireGuard LLC. All Rights Reserved."
      VALUE "OriginalFilename", "wintun.sys"
      VALUE "ProductName", "Wintun Driver"
      VALUE "ProductVersion", EXPAND(WINTUN_VERSION)
      VALUE "Comments", "https://www.wintun.net/"
    END
  END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x409, 1200
  END
END
