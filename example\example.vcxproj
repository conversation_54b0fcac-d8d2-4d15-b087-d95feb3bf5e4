<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2abac503-245d-4f53-85c5-0f844def738b}</ProjectGuid>
    <RootNamespace>example</RootNamespace>
    <ProjectName>example</ProjectName>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>WindowsApplicationForDrivers10.0</PlatformToolset>
    <ForcedTargetVersion>Windows10</ForcedTargetVersion>
  </PropertyGroup>
  <Import Project="..\wintun.props" />
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions>_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>..\api</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <AdditionalDependencies>iphlpapi.lib;kernel32.lib;ntdll.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="example.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\api\api.vcxproj">
      <Project>{897f02e3-3eaa-40af-a6dc-17eb2376edaf}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="..\wintun.props.user" Condition="exists('..\wintun.props.user')" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
