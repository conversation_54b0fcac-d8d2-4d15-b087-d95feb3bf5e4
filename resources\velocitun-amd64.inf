; VelociTun Driver
; Copyright (C) 2024 VelociTun Team. All Rights Reserved.

[Version]
Signature = "$Windows NT$"
Class = Net
ClassGUID = {4D36E972-E325-11CE-BFC1-08002BE10318}
Provider = %VelociTun.CompanyName%
CatalogFile.NT = velocitun.cat
PnpLockdown = 1

[Manufacturer]
%VelociTun.CompanyName% = %VelociTun.Name%, NT$ARCH$

[SourceDisksNames]
1 = %VelociTun.DiskDesc%, "", ,

[SourceDisksFiles]
velocitun.sys = 1

[DestinationDirs]
DefaultDestDir = 12
VelociTun.CopyFiles.Sys = 12

[VelociTun.CopyFiles.Sys]
velocitun.sys, , , 0x00004002 ; COPYFLG_IN_USE_RENAME | COPYFLG_NOSKIP

[VelociTun.NT$ARCH$]
%VelociTun.DeviceDesc% = VelociTun.Install, VelociTun

[VelociTun.Install]
Characteristics = 0x1 ; NCF_VIRTUAL
AddReg = VelociTun.Ndi
AddProperty = VelociTun.Properties
CopyFiles = VelociTun.CopyFiles.Sys
*IfType = 53 ; IF_TYPE_PROP_VIRTUAL
*MediaType = 19 ; NdisMediumIP
*PhysicalMediaType = 0 ; NdisPhysicalMediumUnspecified
EnableDhcp = 0 ; Disable DHCP

[VelociTun.Properties]
DeviceVendorWebsite,,,,"https://github.com/velocitun/velocitun"

[VelociTun.Install.Services]
AddService = velocitun, 2, VelociTun.Service, VelociTun.EventLog ; 2=SPSVCINST_ASSOCSERVICE

[VelociTun.Ndi]
HKR, Ndi, Service, 0, velocitun
HKR, Ndi\Interfaces, UpperRange, , "ndis5"
HKR, Ndi\Interfaces, LowerRange, , "nolower"

[VelociTun.Service]
DisplayName = %VelociTun.Name%
Description = %VelociTun.DeviceDesc%
ServiceType = 1 ; SERVICE_KERNEL_DRIVER
StartType = 3 ; SERVICE_DEMAND_START
ErrorControl = 1 ; SERVICE_ERROR_NORMAL
ServiceBinary = %12%\velocitun.sys

[VelociTun.EventLog]
HKR, , EventMessageFile, 0x00020000, "%11%\IoLogMsg.dll;%12%\velocitun.sys"
HKR, , TypesSupported, 0x00010001, 7

[Strings]
VelociTun.Name = "VelociTun"
VelociTun.DiskDesc = "VelociTun Driver Install Disk"
VelociTun.DeviceDesc = "VelociTun High-Speed Network Tunnel"
VelociTun.CompanyName = "VelociTun Team"
