AccessModifierOffset: -4
AlignAfterOpenBracket: AlwaysBreak
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: DontAlign
AlignOperands: true
AllowAllParametersOfDeclarationOnNextLine: false
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Inline
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterReturnType: TopLevel
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: true
BinPackArguments: false
BinPackParameters: false
BraceWrapping:
    AfterClass: true
    AfterControlStatement: true
    AfterEnum: true
    AfterFunction: true
    AfterNamespace: false
    AfterStruct: true
    AfterUnion: true
    AfterExternBlock: false
    BeforeCatch: true
    BeforeElse: true
BreakBeforeBraces: Custom
BreakBeforeBinaryOperators: None
BreakBeforeTernaryOperators: true
BreakConstructorInitializers: AfterColon
BreakStringLiterals: false
ColumnLimit: 120
CommentPragmas: '^begin_wpp|^end_wpp|^FUNC |^USESUFFIX |^USESUFFIX '
ConstructorInitializerAllOnOneLineOrOnePerLine: true
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4
Cpp11BracedListStyle: false
DerivePointerAlignment: false
ExperimentalAutoDetectBinPacking: false
IndentCaseLabels: false
IndentPPDirectives: AfterHash
IndentWidth: 4
KeepEmptyLinesAtTheStartOfBlocks: false
Language: Cpp
MacroBlockBegin: '^BEGIN_MODULE$|^BEGIN_TEST_CLASS$|^BEGIN_TEST_METHOD$'
MacroBlockEnd: '^END_MODULE$|^END_TEST_CLASS$|^END_TEST_METHOD$'
MaxEmptyLinesToKeep: 1
NamespaceIndentation: None
PointerAlignment: Right
ReflowComments: true
SortIncludes: false
SpaceAfterCStyleCast: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeCtorInitializerColon: true
SpaceBeforeCtorInitializerColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeRangeBasedForLoopColon: true
SpaceInEmptyParentheses: false
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
Standard: Cpp11
StatementMacros: [
    '__drv_allocatesMem',
    '__drv_freesMem',
    '__drv_preferredFunction',
    '__drv_setsIRQL',
    '_Acquires_exclusive_lock_',
    '_Acquires_lock_',
    '_Acquires_shared_lock_',
    '_Acquires_rcu_',
    '_At_',
    '_At_buffer_',
    '_Check_return_',
    '_Dispatch_type_',
    '_Field_size_bytes_',
    '_Function_class_',
    '_Guarded_by_',
    '_IRQL_lowers_',
    '_IRQL_raises_',
    '_IRQL_requires_',
    '_IRQL_requires_max_',
    '_IRQL_requires_min_',
    '_IRQL_requires_same_',
    '_IRQL_restores_',
    '_IRQL_restores_global_',
    '_IRQL_saves_',
    '_IRQL_saves_global_',
    '_Must_inspect_result_',
    '_Out_writes_bytes_all_',
    '_Post_equals_last_error_',
    '_Post_maybenull_',
    '_Post_notnull_',
    '_Post_writable_byte_size_',
    '_Releases_exclusive_lock_',
    '_Releases_lock_',
    '_Releases_shared_lock_',
    '_Releases_rcu_',
    '_Requires_shared_lock_held_',
    '_Requires_exclusive_lock_held_',
    '_Requires_lock_held_',
    '_Requires_lock_not_held_',
    '_Requires_rcu_held_',
    '_Ret_bytecount_',
    '_Ret_maybenull_',
    '_Ret_range_',
    '_Ret_writes_bytes_',
    '_Return_type_success_',
    '_Success_',
    '_Use_decl_annotations_',
    '_When_',
    'EXTERN_C',
    'INITCODE',
    'NONPAGED',
    'PAGED',
    'PAGEDX',
    'PNPCODE'
    ]
TabWidth: '4'
UseTab: Never
