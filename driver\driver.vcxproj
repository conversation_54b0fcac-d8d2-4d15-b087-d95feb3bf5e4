<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F7679B65-2FEC-469A-8BAC-B07BF4439422}</ProjectGuid>
    <RootNamespace>driver</RootNamespace>
    <ProjectName>driver</ProjectName>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>WDM</DriverType>
  </PropertyGroup>
  <Import Project="..\wintun.props" />
  <PropertyGroup>
    <TargetName>wintun</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions>NDIS_MINIPORT_DRIVER=1;NDIS620_MINIPORT=1;NDIS683_MINIPORT=1;NDIS_WDM=1;POOL_ZERO_DOWN_LEVEL_SUPPORT;POOL_NX_OPTIN=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/volatile:iso %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>ndis.lib;wdmsec.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <Inf>
      <TimeStamp>$(WintunVersion)</TimeStamp>
    </Inf>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <Inf>
      <TimeStamp>*</TimeStamp>
    </Inf>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="wintun.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="wintun.rc" />
  </ItemGroup>
  <ItemGroup>
    <Inf Include="wintun.inf" />
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="undocumented.h" />
  </ItemGroup>
  <Import Project="..\wintun.props.user" Condition="exists('..\wintun.props.user')" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
