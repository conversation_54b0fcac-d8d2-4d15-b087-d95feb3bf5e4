﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  SPDX-License-Identifier: GPL-2.0

  Copyright (C) 2018-2021 WireGuard LLC. All Rights Reserved.
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition="'$(Configuration)'==''">Release</Configuration>
  </PropertyGroup>

  <!--
    General Properties
  -->
  <Import Project="wintun.props" />
  <Import Project="wintun.props.user" Condition="exists('wintun.props.user')" />

  <Target Name="Driver" DependsOnTargets="Driver-x86;Driver-amd64;Driver-arm64" />
  <Target Name="Dll"    DependsOnTargets="Dll-x86;Dll-amd64;Dll-arm64" />
  <Target Name="Clean">
    <RemoveDir Directories="$(Configuration)\amd64\" />
    <RemoveDir Directories="$(Configuration)\arm\" />
    <RemoveDir Directories="$(Configuration)\arm64\" />
    <RemoveDir Directories="$(Configuration)\x86\" />
    <RemoveDir Directories="dist\" />
  </Target>

  <!--
    Driver Building
    Note: Use explicit Inputs/Outputs as WindowsDriver.Common.targets triggers driver re-packaging and signing on every invocation.
  -->
  <PropertyGroup>
    <DriverInputs>driver\undocumented.h;driver\wintun.c;driver\wintun.inf;wintun.props;driver\wintun.rc;driver\driver.vcxproj;$(DriverInputs)</DriverInputs>
  </PropertyGroup>
  <Target Name="Driver-x86"
    Outputs="$(Configuration)\x86\driver\wintun.sys;$(Configuration)\x86\driver\wintun.inf;$(Configuration)\x86\driver\wintun.cat"
    Inputs="$(DriverInputs)">
    <MSBuild Projects="driver\driver.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=Win32" />
  </Target>
  <Target Name="Driver-amd64"
    Outputs="$(Configuration)\amd64\driver\wintun.sys;$(Configuration)\amd64\driver\wintun.inf;$(Configuration)\amd64\driver\wintun.cat"
    Inputs="$(DriverInputs)">
    <MSBuild Projects="driver\driver.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=x64" />
  </Target>
  <Target Name="Driver-arm"
    Outputs="$(Configuration)\arm\driver\wintun.sys;$(Configuration)\arm\driver\wintun.inf;$(Configuration)\arm\driver\wintun.cat"
    Inputs="$(DriverInputs)">
    <MSBuild Projects="driver\driver.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=ARM" />
  </Target>
  <Target Name="Driver-arm64"
    Outputs="$(Configuration)\arm64\driver\wintun.sys;$(Configuration)\arm64\driver\wintun.inf;$(Configuration)\arm64\driver\wintun.cat"
    Inputs="$(DriverInputs)">
    <MSBuild Projects="driver\driver.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=ARM64" />
  </Target>

  <!--
    wintun.dll Building
  -->
  <Target Name="Dll-x86"
    Outputs="$(Configuration)\x86\wintun.dll"
    DependsOnTargets="Dll-amd64;Dll-arm64">
    <MSBuild Projects="setupapihost\setupapihost.vcxproj;api\api.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=Win32" />
  </Target>
  <Target Name="Dll-amd64"
    Outputs="$(Configuration)\amd64\wintun.dll"
    DependsOnTargets="Dll-arm64">
    <MSBuild Projects="setupapihost\setupapihost.vcxproj;api\api.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=x64" />
  </Target>
  <Target Name="Dll-arm"
    Outputs="$(Configuration)\arm\wintun.dll"
    DependsOnTargets="Dll-arm64">
    <MSBuild Projects="setupapihost\setupapihost.vcxproj;api\api.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=ARM" />
  </Target>
  <Target Name="Dll-arm64"
    Outputs="$(Configuration)\arm64\wintun.dll">
    <MSBuild Projects="setupapihost\setupapihost.vcxproj;api\api.vcxproj" Targets="Build" Properties="Configuration=$(Configuration);Platform=ARM64" />
  </Target>

  <!--
    Zip Building
  -->
  <PropertyGroup>
    <ZipTargetPath>dist\wintun-$(WintunVersion).zip</ZipTargetPath>
    <ZipIntDir>dist\zip-intermediate\</ZipIntDir>
  </PropertyGroup>
  <ItemGroup>
    <ZipFilesSrc Include="prebuilt-binaries-license.txt"     /><ZipFilesDst Include="$(ZipIntDir)wintun\LICENSE.txt"          />
    <ZipFilesSrc Include="README.md"                         /><ZipFilesDst Include="$(ZipIntDir)wintun\README.md"            />
    <ZipFilesSrc Include="api\wintun.h"                      /><ZipFilesDst Include="$(ZipIntDir)wintun\include\wintun.h"     />
    <ZipFilesSrc Include="$(Configuration)\amd64\wintun.dll" /><ZipFilesDst Include="$(ZipIntDir)wintun\bin\amd64\wintun.dll" />
    <ZipFilesSrc Include="$(Configuration)\arm64\wintun.dll" /><ZipFilesDst Include="$(ZipIntDir)wintun\bin\arm64\wintun.dll" />
    <ZipFilesSrc Include="$(Configuration)\x86\wintun.dll"   /><ZipFilesDst Include="$(ZipIntDir)wintun\bin\x86\wintun.dll"   />
  </ItemGroup>
  <Target Name="Zip"
    Inputs="@(ZipFilesSrc)"
    Outputs="$(ZipTargetPath)"
    DependsOnTargets="Dll">
    <RemoveDir Directories="$(ZipIntDir)" />
    <Copy SourceFiles="@(ZipFilesSrc)" DestinationFiles="@(ZipFilesDst)" />
    <ZipDirectory DestinationFile="$(ZipTargetPath)" Overwrite="true" SourceDirectory="$(ZipIntDir)" />
    <RemoveDir Directories="$(ZipIntDir)" />
    <GetFileHash Files="$(ZipTargetPath)" Algorithm="SHA256" HashEncoding="hex">
      <Output TaskParameter="Items" ItemName="InstallerLibraryHash" />
    </GetFileHash>
    <Message Text="SHA256(&quot;$(ZipTargetPath)&quot;) = @(InstallerLibraryHash->Metadata('FileHash')->ToLower())"/>
  </Target>
</Project>
