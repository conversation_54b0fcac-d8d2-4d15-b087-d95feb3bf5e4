Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28922.388
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "example", "example\example.vcxproj", "{2ABAC503-245D-4F53-85C5-0F844DEF738B}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "api", "api\api.vcxproj", "{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}"
	ProjectSection(ProjectDependencies) = postProject
		{F7679B65-2FEC-469A-8BAC-B07BF4439422} = {F7679B65-2FEC-469A-8BAC-B07BF4439422}
		{9911D673-CF5F-4B41-B190-807AA1BE445B} = {9911D673-CF5F-4B41-B190-807AA1BE445B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "driver", "driver\driver.vcxproj", "{F7679B65-2FEC-469A-8BAC-B07BF4439422}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "setupapihost", "setupapihost\setupapihost.vcxproj", "{9911D673-CF5F-4B41-B190-807AA1BE445B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{3A98F138-EE02-4488-B856-B3C48500BEA8}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		wintun.proj = wintun.proj
		wintun.props = wintun.props
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|amd64 = Debug|amd64
		Debug|arm = Debug|arm
		Debug|arm64 = Debug|arm64
		Debug|x86 = Debug|x86
		Release|amd64 = Release|amd64
		Release|arm = Release|arm
		Release|arm64 = Release|arm64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|amd64.ActiveCfg = Debug|x64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|amd64.Build.0 = Debug|x64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|arm.ActiveCfg = Debug|ARM
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|arm.Build.0 = Debug|ARM
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|arm64.ActiveCfg = Debug|ARM64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|arm64.Build.0 = Debug|ARM64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|x86.ActiveCfg = Debug|Win32
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Debug|x86.Build.0 = Debug|Win32
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|amd64.ActiveCfg = Release|x64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|amd64.Build.0 = Release|x64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|arm.ActiveCfg = Release|ARM
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|arm.Build.0 = Release|ARM
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|arm64.ActiveCfg = Release|ARM64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|arm64.Build.0 = Release|ARM64
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|x86.ActiveCfg = Release|Win32
		{2ABAC503-245D-4F53-85C5-0F844DEF738B}.Release|x86.Build.0 = Release|Win32
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|amd64.ActiveCfg = Debug|x64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|amd64.Build.0 = Debug|x64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|arm.ActiveCfg = Debug|ARM
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|arm.Build.0 = Debug|ARM
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|arm64.ActiveCfg = Debug|ARM64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|arm64.Build.0 = Debug|ARM64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|x86.ActiveCfg = Debug|Win32
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Debug|x86.Build.0 = Debug|Win32
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|amd64.ActiveCfg = Release|x64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|amd64.Build.0 = Release|x64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|arm.ActiveCfg = Release|ARM
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|arm.Build.0 = Release|ARM
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|arm64.ActiveCfg = Release|ARM64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|arm64.Build.0 = Release|ARM64
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|x86.ActiveCfg = Release|Win32
		{897F02E3-3EAA-40AF-A6DC-17EB2376EDAF}.Release|x86.Build.0 = Release|Win32
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|amd64.ActiveCfg = Debug|x64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|amd64.Build.0 = Debug|x64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|arm.ActiveCfg = Debug|ARM
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|arm.Build.0 = Debug|ARM
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|arm64.ActiveCfg = Debug|ARM64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|arm64.Build.0 = Debug|ARM64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|x86.ActiveCfg = Debug|Win32
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Debug|x86.Build.0 = Debug|Win32
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|amd64.ActiveCfg = Release|x64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|amd64.Build.0 = Release|x64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|arm.ActiveCfg = Release|ARM
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|arm.Build.0 = Release|ARM
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|arm64.ActiveCfg = Release|ARM64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|arm64.Build.0 = Release|ARM64
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|x86.ActiveCfg = Release|Win32
		{F7679B65-2FEC-469A-8BAC-B07BF4439422}.Release|x86.Build.0 = Release|Win32
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|amd64.ActiveCfg = Debug|x64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|amd64.Build.0 = Debug|x64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|arm.ActiveCfg = Debug|ARM
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|arm.Build.0 = Debug|ARM
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|arm64.ActiveCfg = Debug|ARM64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|arm64.Build.0 = Debug|ARM64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|x86.ActiveCfg = Debug|Win32
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Debug|x86.Build.0 = Debug|Win32
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|amd64.ActiveCfg = Release|x64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|amd64.Build.0 = Release|x64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|arm.ActiveCfg = Release|ARM
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|arm.Build.0 = Release|ARM
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|arm64.ActiveCfg = Release|ARM64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|arm64.Build.0 = Release|ARM64
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|x86.ActiveCfg = Release|Win32
		{9911D673-CF5F-4B41-B190-807AA1BE445B}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {254563AF-E0B3-48F0-B564-A45AEC95591F}
	EndGlobalSection
EndGlobal
