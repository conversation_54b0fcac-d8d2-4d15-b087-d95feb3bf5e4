use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VelociTunResult};
use windows::Win32::Foundation::*;
use windows::Win32::System::Registry::*;

pub fn get_registry_string(hkey: HKEY, subkey: &str, value_name: &str) -> VelociTunResult<String> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegOpenKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            KEY_READ,
            &mut key_handle,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let mut data_type = REG_NONE;
        let mut data_size = 0u32;

        // Get the size first
        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            None,
            Some(&mut data_size),
        );

        if result != ERROR_SUCCESS {
            RegCloseKey(key_handle);
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        if data_type != REG_SZ && data_type != REG_EXPAND_SZ {
            RegCloseKey(key_handle);
            return Err(VelociTunError::InvalidParameter(
                "Registry value is not a string".to_string(),
            ));
        }

        let mut buffer = vec![0u16; (data_size / 2) as usize];

        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            Some(buffer.as_mut_ptr() as *mut u8),
            Some(&mut data_size),
        );

        RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        // Remove null terminator if present
        if let Some(pos) = buffer.iter().position(|&x| x == 0) {
            buffer.truncate(pos);
        }

        Ok(String::from_utf16_lossy(&buffer))
    }
}

pub fn set_registry_string(
    hkey: HKEY,
    subkey: &str,
    value_name: &str,
    value: &str,
) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegCreateKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            windows::core::PCWSTR::null(),
            REG_OPTION_NON_VOLATILE,
            KEY_WRITE,
            None,
            &mut key_handle,
            None,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let result = RegSetValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            0,
            REG_SZ,
            Some(value.as_bytes()),
        );

        RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

pub fn get_registry_dword(hkey: HKEY, subkey: &str, value_name: &str) -> VelociTunResult<u32> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegOpenKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            KEY_READ,
            &mut key_handle,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let mut data_type = REG_NONE;
        let mut data: u32 = 0;
        let mut data_size = std::mem::size_of::<u32>() as u32;

        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            Some(&mut data as *mut u32 as *mut u8),
            Some(&mut data_size),
        );

        RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        if data_type != REG_DWORD {
            return Err(VelociTunError::InvalidParameter(
                "Registry value is not a DWORD".to_string(),
            ));
        }

        Ok(data)
    }
}

pub fn set_registry_dword(
    hkey: HKEY,
    subkey: &str,
    value_name: &str,
    value: u32,
) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegCreateKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            windows::core::PCWSTR::null(),
            REG_OPTION_NON_VOLATILE,
            KEY_WRITE,
            None,
            &mut key_handle,
            None,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let result = RegSetValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            0,
            REG_DWORD,
            Some(&value.to_le_bytes()),
        );

        RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

pub fn delete_registry_key(hkey: HKEY, subkey: &str) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();

    unsafe {
        let result = RegDeleteKeyW(hkey, windows::core::PCWSTR(subkey_wide.as_ptr()));

        if result != ERROR_SUCCESS && result != ERROR_FILE_NOT_FOUND {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}
